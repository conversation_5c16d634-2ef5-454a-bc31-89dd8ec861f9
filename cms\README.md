目录结构

cms/
├── backend/                 # Strapi 后端项目 (Headless CMS)
│   ├── config/
│   │   ├── database.js      # 数据库配置
│   │   └── server.js        # 服务器配置 (端口, 中间件等)
│   ├── src/
│   │   ├── api/            # 内容类型 (Content-Type) 定义和配置
│   │   ├── admin/          # Strapi 后台的扩展配置
│   │   ├── components/     # 可复用组件
│   │   └── index.js        # Strapi 自定义启动逻辑
│   ├── .env                # 环境变量 (数据库URL, JWT密钥等)
│   └── package.json
│
└── frontend/                # Next.js 前端项目
    ├── pages/
    │   ├── index.js        # 首页，使用 getStaticProps
    │   ├── [slug].js       # 动态路由，用于文章详情页
    │   └── ...             # 其他页面
    ├── components/         # React 组件 (Header, Footer, ArticleCard)
    ├── styles/             # CSS 模块或全局样式
    ├── lib/                # 工具函数 (API 请求客户端)
    ├── public/             # 静态资源
    ├── .env.local          # 环境变量 (NEXT_PUBLIC_STRAPI_URL)
    └── package.json


最核心必备的功能模块
一个门户网站管理系统的核心功能可以浓缩为以下四点：

模块	核心功能点	说明
1. 用户与权限	- 管理员认证
- 角色管理 (RBAC)
- 操作日志	最基础的安全保障。区分超级管理员、内容编辑、投稿员等角色。
2. 内容管理	- 栏目/分类管理
- 文章/内容管理 (增删改查)
- 标签管理
- 媒体库 (图片/文件上传)	核心中的核心。支持富文本（Markdown更好，更轻量）、封面图、发布时间等基础字段。
3. 模板管理	- 模板引擎集成	不是可视化编辑，而是指开发者在代码中使用 Vue/React 组件构建模板，通过API获取内容填充。管理后台只需配置“模板类型”即可。
4. 发布与部署	- 一键触发构建	在后台提供一个“清除缓存”或“重新构建”的按钮。点击后，通过Webhook自动通知Vercel/Netlify等平台重新拉取内容并生成静态页面。


1. Strapi 后端配置 (backend/config/database.js)
使用 PostgreSQL (推荐用于生产环境，SQLite 仅用于开发)

javascript
// 使用环境变量，避免将敏感信息硬编码
module.exports = ({ env }) => ({
  connection: {
    client: 'postgresql',
    connection: {
      host: env('DATABASE_HOST', 'localhost'),
      port: env.int('DATABASE_PORT', 5432),
      database: env('DATABASE_NAME', 'strapi'),
      user: env('DATABASE_USERNAME', 'strapi'),
      password: env('DATABASE_PASSWORD', 'password'),
      ssl: env.bool('DATABASE_SSL', false) && {
        rejectUnauthorized: env.bool('DATABASE_SSL_REJECT_UNAUTHORIZED', true),
      },
    },
    pool: {
      min: env.int('DATABASE_POOL_MIN', 2),
      max: env.int('DATABASE_POOL_MAX', 10),
    },
  },
});
2. Strapi 服务器配置 (backend/config/server.js)
javascript
module.exports = ({ env }) => ({
  host: env('HOST', '0.0.0.0'),
  port: env.int('PORT', 1337),
  // 在生产环境中配置反向代理（如 Nginx）时非常有用
  url: env('PUBLIC_URL', 'http://localhost:1337'),
  app: {
    keys: env.array('APP_KEYS', ['myKeyA', 'myKeyB']), // 必须更改！用于加密会话
  },
});
3. Next.js 环境变量 (frontend/.env.local)
bash
# 指向 Strapi 后端 API
NEXT_PUBLIC_STRAPI_API_URL=https://your-strapi-app.com/api
# 仅为服务器端构建过程使用（例如在 getStaticProps 中）
STRAPI_PREVIEW_SECRET=your_secret_preview_token
4. Next.js API 请求客户端 (frontend/lib/api.js)
javascript
// 一个简单的函数，用于获取 Strapi 数据
export async function getStrapiData(path, queryParams = '') {
  const baseUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL;
  const url = new URL(`${baseUrl}${path}${queryParams}`);

  // 使用 populate=* 来获取关联数据（如封面图）
  url.searchParams.append('populate', '*');

  try {
    const response = await fetch(url.toString());
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Fetch from Strapi failed:', error);
    return null;
  }
}
5. Next.js 页面示例 - 首页 (frontend/pages/index.js)
javascript
import Head from 'next/head';
import ArticleCard from '../components/ArticleCard';
import { getStrapiData } from '../lib/api';

export default function Home({ articles }) {
  return (
    <div>
      <Head>
        <title>我的极速门户网站</title>
        <meta name="description" content="由 Strapi 和 Next.js 驱动" />
      </Head>

      <main>
        <h1>最新文章</h1>
        {articles?.data?.map((article) => (
          <ArticleCard key={article.id} article={article} />
        ))}
      </main>
    </div>
  );
}

// 在构建时运行，从 Strapi 获取数据并生成静态页面
export async function getStaticProps() {
  const articlesData = await getStrapiData('/articles', '?sort[0]=publishedAt:desc');
  // 如果获取失败，返回空 props，页面会显示为 404
  if (!articlesData) {
    return {
      notFound: true,
    };
  }
  return {
    props: {
      articles: articlesData,
    },
    // 启用 Incremental Static Regeneration (ISR)
    // 最多每 hour 重新生成一次页面
    revalidate: 3600, 
  };
}